# Smash Music 🎵

A modern music application built with Next.js, featuring dark/light theme support and internationalization.

## 🚀 Tech Stack

### Core Framework
- **[Next.js 15.3.3](https://nextjs.org)** - React framework with App Router
- **[React 19](https://react.dev)** - UI library
- **[TypeScript 5](https://www.typescriptlang.org)** - Type safety

### Styling & UI
- **[Tailwind CSS v4](https://tailwindcss.com)** - Utility-first CSS framework
- **[shadcn/ui](https://ui.shadcn.com)** - Re-usable component library
- **[Lucide React](https://lucide.dev)** - Icon library
- **[Radix UI](https://www.radix-ui.com)** - Headless UI primitives

### Features
- **[next-intl](https://next-intl-docs.vercel.app)** - Internationalization (i18n)
- **Custom Theme System** - Dark/light mode with system preference detection
- **[<PERSON><PERSON><PERSON> Font](https://vercel.com/font)** - Modern typography

### Development Tools
- **[ESLint](https://eslint.org)** - Code linting
- **[pnpm](https://pnpm.io)** - Fast, disk space efficient package manager

## 📁 Project Structure

```
src/
├── app/                    # Next.js App Router
│   ├── globals.css        # Global styles and CSS variables
│   ├── layout.tsx         # Root layout with providers
│   ├── page.tsx          # Home page
│   └── favicon.ico       # App icon
├── components/            # Shared/reusable components
│   ├── ui/               # shadcn/ui components
│   │   ├── avatar.tsx
│   │   ├── badge.tsx
│   │   ├── button.tsx
│   │   ├── card.tsx
│   │   └── index.ts
│   ├── shared/           # Custom shared components
│   ├── ThemeToggle.tsx   # Theme switching component
│   ├── ClientThemeToggle.tsx
│   ├── DynamicThemeToggle.tsx
│   └── index.ts
├── contexts/             # React contexts
│   └── ThemeContext.tsx  # Theme management
├── features/             # Feature-based architecture
│   ├── home/             # Home page feature
│   │   ├── components/   # Home-specific components
│   │   ├── hooks/        # Home-specific hooks
│   │   ├── types/        # Home-specific types
│   │   └── utils/        # Home-specific utilities
│   └── artist/           # Artist page feature (example)
│       ├── components/   # Artist-specific components
│       ├── hooks/        # Artist-specific hooks
│       ├── types/        # Artist-specific types
│       └── utils/        # Artist-specific utilities
├── hooks/                # Shared hooks
├── i18n/                # Internationalization
│   └── request.ts       # i18n configuration
├── lib/                 # Shared utilities
│   └── utils.ts         # Common utility functions
└── types/               # Shared types

messages/                # Translation files
├── en.json             # English translations
├── es.json             # Spanish translations
└── fr.json             # French translations

public/                 # Static assets
├── *.svg              # SVG icons and images
└── favicon.ico        # Favicon
```

## 🛠️ Getting Started

### Prerequisites
- Node.js 18+
- pnpm (recommended) or npm/yarn

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd smash-music
   ```

2. **Install dependencies**
   ```bash
   pnpm install
   ```

3. **Run the development server**
   ```bash
   pnpm dev
   ```

4. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

### Available Scripts

```bash
pnpm dev          # Start development server with Turbopack
pnpm build        # Build for production
pnpm start        # Start production server
pnpm lint         # Run ESLint
```

## 🎨 Theme System

The application includes a comprehensive theme system with:

- **Light/Dark mode toggle**
- **System preference detection**
- **Persistent theme storage**
- **CSS custom properties** for consistent theming
- **Tailwind CSS integration** with `dark:` variants

### Theme Usage

```tsx
import { useTheme } from '@/contexts/ThemeContext';

function MyComponent() {
  const { theme, toggleTheme, setTheme } = useTheme();

  return (
    <button onClick={toggleTheme}>
      Current theme: {theme}
    </button>
  );
}
```

## 🌍 Internationalization

Built-in i18n support with next-intl:

- **Multiple languages**: English, Spanish, French
- **No routing required** - uses next-intl without route-based localization
- **Type-safe translations**
- **Easy to extend** with additional languages

### Adding Translations

1. Add your translations to `messages/{locale}.json`
2. Use the `useTranslations` hook:

```tsx
import { useTranslations } from 'next-intl';

function MyComponent() {
  const t = useTranslations('HomePage');

  return <h1>{t('title')}</h1>;
}
```

## 🧩 Component Architecture

### shadcn/ui Integration

The project uses shadcn/ui components with:
- **New York style** variant
- **CSS variables** for theming
- **Lucide icons**
- **Full TypeScript support**

### Adding New Components

```bash
# Add a new shadcn/ui component
npx shadcn@latest add [component-name]
```

## 📦 Features-Based Architecture

This project uses a **features-based architecture** for scalability and maintainability. Each feature/page has its own dedicated folder with all related code.

### Example Feature Structure:

```
src/features/[feature-name]/
├── components/           # Feature-specific components
│   ├── FeatureComponent.tsx
│   └── index.ts
├── hooks/               # Feature-specific hooks
│   ├── useFeature.ts
│   └── index.ts
├── types/               # Feature-specific types
│   ├── feature.types.ts
│   └── index.ts
└── utils/               # Feature-specific utilities
    ├── feature.utils.ts
    └── index.ts
```

### Benefits:
- **Co-location**: Related code stays together
- **Scalability**: Easy to add new features without conflicts
- **Maintainability**: Clear separation of concerns
- **Team Collaboration**: Multiple developers can work on different features

📖 **[Read the complete Architecture Guide](./ARCHITECTURE.md)** for detailed information on how to add new features and best practices.

## 🚀 Deployment

### Vercel (Recommended)

1. Push your code to GitHub
2. Connect your repository to [Vercel](https://vercel.com)
3. Deploy automatically on every push

### Other Platforms

The app can be deployed to any platform that supports Next.js:
- Netlify
- Railway
- DigitalOcean App Platform
- AWS Amplify

## 📝 Development Guidelines

### Code Organization
- Use **feature-based architecture** for scalability
- Keep **shared components** in `src/components/`
- Place **page-specific logic** in respective feature folders
- Follow **TypeScript best practices**

### Styling
- Use **Tailwind CSS** for styling
- Leverage **CSS custom properties** for theming
- Prefer **shadcn/ui components** for consistency

### State Management
- Use **React Context** for global state
- Implement **custom hooks** for feature-specific logic
- Keep **component state local** when possible

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Commit your changes: `git commit -m 'Add amazing feature'`
4. Push to the branch: `git push origin feature/amazing-feature`
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

---

Built with ❤️ using Next.js and modern web technologies.
